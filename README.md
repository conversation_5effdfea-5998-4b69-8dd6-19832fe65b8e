# ”2025江苏省大学新生安全知识教育“一键完成脚本
jiangsu-safety-platform-skip

**🤔 食用指导**

1.安装 Python3 ，并且确保安装了这个额外的库： requests 。

2.手机打开微信客户端，访问 http://wap.xiaoyuananquantong.com/guns-vip-main/wap/wapJSLogin 完成登录进入主页后点击右上角复制链接。

3.复制的链接中你需要选择从 "userid=" 后的一串数字，不包含除数字以外的任何成分。

4.将这串数字输入到程序中，然后回车，直到程序运行完成，你可以在平台主页的结课选项中查询到你的证书。

⚙ **基本原理**

通过数据包重放的方式完成课程学习，通过将考题对应答案写入 database.db 中来实现答案获取和处理。

✒️ **进阶**

欢迎提交 Issue 来交换您的看法和对脚本的更多建议！

——南晓25届新生 Scwizard
